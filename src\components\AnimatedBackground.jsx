import React, { useEffect, useRef } from 'react';

const AnimatedBackground = () => {
  const waterHighlightsRef = useRef(null);
  const particleContainerRef = useRef(null);
  const waterBackgroundRef = useRef(null);
  const mouseX = useRef(window.innerWidth / 2);
  const mouseY = useRef(window.innerHeight / 2);

  useEffect(() => {
    const waterHighlights = waterHighlightsRef.current;
    const particleContainer = particleContainerRef.current;
    const waterBackground = waterBackgroundRef.current;

    if (!waterHighlights || !particleContainer || !waterBackground) return;

    // Mouse move handler
    const handleMouseMove = (event) => {
      mouseX.current = event.clientX;
      mouseY.current = event.clientY;
    };

    // Update highlight position
    const updateHighlightPosition = () => {
      const targetX = (mouseX.current / window.innerWidth) * 100;
      const targetY = (mouseY.current / window.innerHeight) * 100;

      const currentX = parseFloat(waterHighlights.style.getPropertyValue('--highlight-x') || 50);
      const currentY = parseFloat(waterHighlights.style.getPropertyValue('--highlight-y') || 50);

      const lerpFactor = 0.08; // Increased for more responsive feel
      const easingFactor = 1 - Math.pow(1 - lerpFactor, 2); // Quadratic easing

      const newX = currentX + (targetX - currentX) * easingFactor;
      const newY = currentY + (targetY - currentY) * easingFactor;

      // Add subtle pulsing effect
      const pulseIntensity = 0.7 + 0.1 * Math.sin(Date.now() * 0.003);

      waterHighlights.style.setProperty('--highlight-x', `${newX}%`);
      waterHighlights.style.setProperty('--highlight-y', `${newY}%`);
      waterHighlights.style.setProperty('--highlight-opacity', pulseIntensity);
    };

    // Create particle
    const createParticle = (x, y) => {
      const particle = document.createElement('div');
      particle.classList.add('water-particle');
      particle.style.left = `${x}px`;
      particle.style.top = `${y}px`;

      // Enhanced random end position with smoother distribution
      const angle = Math.random() * Math.PI * 2;
      const distance = 50 + Math.random() * 80;
      const endX = Math.cos(angle) * distance;
      const endY = Math.sin(angle) * distance;

      // Add initial scale and rotation for more dynamic effect
      const initialScale = 0.8 + Math.random() * 0.4;
      const initialRotation = Math.random() * 360;

      particle.style.transform = `scale(${initialScale}) rotate(${initialRotation}deg)`;
      particle.style.setProperty('--particle-end-x', `${endX}px`);
      particle.style.setProperty('--particle-end-y', `${endY}px`);

      particleContainer.appendChild(particle);

      particle.addEventListener('animationend', () => {
        particle.remove();
      });
    };

    let lastParticleTime = 0;
    const particleInterval = 80; // Reduced for smoother particle generation

    const handleMouseMoveParticles = (event) => {
      const currentTime = Date.now();
      if (currentTime - lastParticleTime > particleInterval) {
        createParticle(
          event.clientX + (Math.random() - 0.5) * 10,
          event.clientY + (Math.random() - 0.5) * 10
        );
        lastParticleTime = currentTime;
      }
    };

    // Animation loop
    const animate = () => {
      updateHighlightPosition();
      requestAnimationFrame(animate);
    };

    // Add event listeners
    waterBackground.addEventListener('mousemove', handleMouseMove);
    waterBackground.addEventListener('mousemove', handleMouseMoveParticles);

    // Start animation
    animate();

    // Cleanup
    return () => {
      waterBackground.removeEventListener('mousemove', handleMouseMove);
      waterBackground.removeEventListener('mousemove', handleMouseMoveParticles);
    };
  }, []);

  return (
    <div
      ref={waterBackgroundRef}
      className="water-background fixed inset-0 -z-10 overflow-hidden"
    >
      <div className="water-layer layer-1"></div>
      <div className="water-layer layer-2"></div>
      <div className="water-layer layer-3"></div>
      <div
        ref={waterHighlightsRef}
        className="water-highlights"
      ></div>
      <div
        ref={particleContainerRef}
        className="particle-container"
      ></div>
    </div>
  );
};

export default AnimatedBackground;
