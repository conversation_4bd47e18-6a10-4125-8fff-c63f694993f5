import { createContext, useState, useContext } from 'react';

// Create the context
const FileContext = createContext();

// FileProvider component to wrap your app and provide the context
export const FileProvider = ({ children }) => {
  const [outputFile, setOutputFile] = useState([]);
  const [inputFiles, setInputFiles] = useState([]);

    
  return (
    <FileContext.Provider value={{ outputFile, setOutputFile , inputFiles , setInputFiles }}>
      {children}
    </FileContext.Provider>
  );
};

// Custom hook to use the FileContext
export const useFileContext = () => useContext(FileContext);
