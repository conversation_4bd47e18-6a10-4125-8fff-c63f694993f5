import React from 'react';

// Clean, functional Glass Effect component with popup animation support
const GlassEffect = ({
  children,
  className = '',
  variant = 'default',
  onClick,
  disabled = false,
  as: Component = 'div',
  popup = false,
  popupDelay = '0s',
  ...props
}) => {
  // Clean variant class mapping
  const variantClasses = {
    button: 'glass-btn',
    card: 'glass-card',
    input: 'glass-input',
    default: ''
  };

  // Build clean class string with popup support
  const classes = [
    'glass',
    variantClasses[variant],
    popup && 'popup-enter',
    className,
    disabled && 'glass-disabled'
  ].filter(Boolean).join(' ');

  // Handle popup animation delay
  const style = popup && popupDelay !== '0s' ? { animationDelay: popupDelay } : {};

  return (
    <Component
      className={classes}
      style={{ ...style, ...props.style }}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      {...props}
    >
      <div className="glass-bg" />
      <div className="glass-content">
        {children}
      </div>
    </Component>
  );
};

// Simple, clean SVG filter
export const GlassFilter = () => (
  <svg className="glass-filter">
    <defs>
      <filter id="glass-blur" x="-50%" y="-50%" width="200%" height="200%">
        <feTurbulence
          type="fractalNoise"
          baseFrequency="0.02"
          numOctaves="3"
          result="noise"
        />
        <feDisplacementMap
          in="SourceGraphic"
          in2="noise"
          scale="8"
        />
      </filter>
    </defs>
  </svg>
);

export default GlassEffect;
