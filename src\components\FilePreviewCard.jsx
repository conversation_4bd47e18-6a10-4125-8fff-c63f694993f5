import { useEffect, useState, useCallback } from 'react';
import * as XLSX from 'xlsx';
import { Download, Eye, EyeClosed, FileText } from 'lucide-react';
import GlassEffect from './GlassEffect';

const FilePreviewCard = ({ fileBlob, fileIndex, input_file }) => {
  const [data, setData] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showDataPreview, setShowDataPreview] = useState(false);
  const [showInputPreview, setShowInputPreview] = useState(false);
  const [inputURL, setInputURL] = useState(null);

  const fileName = input_file?.name || `File ${fileIndex + 1}`;
  const fileType = input_file?.type || '';
  const fileSize = input_file?.size || 0;

  // File type detection
  const isExcel = fileType.includes('spreadsheet') || fileName.match(/\.(xlsx?|csv)$/i);
  const isImage = fileType.startsWith('image/');
  const isPdf = fileType === 'application/pdf';
  const isText = fileType.startsWith('text/');

  // Create input file URL for preview
  useEffect(() => {
    if (!input_file) return;

    const url = URL.createObjectURL(input_file);
    setInputURL(url);

    return () => URL.revokeObjectURL(url);
  }, [input_file]);

  // Parse Excel data
  useEffect(() => {
    console.log('🔍 FilePreviewCard - Checking file:', {
      fileName,
      fileType,
      isExcel,
      hasBlobData: !!fileBlob,
      blobSize: fileBlob?.size,
      blobType: fileBlob?.type
    });

    if (!fileBlob) {
      console.log('❌ No file blob provided');
      setData([]);
      return;
    }

    // Try to parse any file that might contain CSV data
    const canParse = isExcel ||
                    fileType.includes('csv') ||
                    fileName.toLowerCase().includes('csv') ||
                    fileBlob.type.includes('csv') ||
                    fileBlob.type === 'text/csv' ||
                    // Also try to parse any blob that might be a CSV result from processing
                    fileBlob.type === 'text/csv' ||
                    fileBlob.size < 1000000; // Try to parse smaller files (likely CSV results)

    if (!canParse) {
      console.log('📄 File type not parseable as CSV:', fileType);
      console.log('📄 Will show file info instead');
      setData([]);
      return;
    }

    console.log('🚀 Starting to parse file...');
    setLoading(true);
    setError(null);

    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        console.log('📖 File read successfully, parsing...');

        // First try to parse as text (for CSV content)
        let jsonData = [];

        try {
          // Try as Excel/binary first
          const workbook = XLSX.read(e.target.result, { type: 'array' });
          console.log('📊 Workbook created, sheets:', workbook.SheetNames);

          if (workbook.SheetNames.length > 0) {
            const sheet = workbook.Sheets[workbook.SheetNames[0]];
            jsonData = XLSX.utils.sheet_to_json(sheet);
            console.log('✅ Excel data parsed successfully:', jsonData.length, 'rows');
          }
        } catch (excelError) {
          console.log('📝 Excel parsing failed, trying as CSV text...');

          // Try as CSV text
          const textContent = new TextDecoder().decode(e.target.result);
          const workbook = XLSX.read(textContent, { type: 'string' });

          if (workbook.SheetNames.length > 0) {
            const sheet = workbook.Sheets[workbook.SheetNames[0]];
            jsonData = XLSX.utils.sheet_to_json(sheet);
            console.log('✅ CSV text parsed successfully:', jsonData.length, 'rows');
          }
        }

        if (jsonData.length === 0) {
          throw new Error('No data found in file');
        }

        console.log('📋 Sample data:', jsonData.slice(0, 2));
        setData(jsonData);
        setError(null);
      } catch (err) {
        console.error('❌ Parse error:', err);
        setError(`Failed to parse file: ${err.message}`);
        setData([]);
      } finally {
        setLoading(false);
      }
    };

    reader.onerror = (err) => {
      console.error('❌ File read error:', err);
      setError('Failed to read file');
      setLoading(false);
    };

    reader.readAsArrayBuffer(fileBlob);
  }, [fileBlob, isExcel, fileType, fileName]);

  // Download function
  const downloadFile = useCallback(() => {
    if (!data.length) return;

    try {
      const sheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1');

      const csvData = XLSX.write(workbook, { bookType: 'csv', type: 'array' });
      const blob = new Blob([csvData], { type: 'text/csv' });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${fileName.replace(/\.[^/.]+$/, '')}_processed.csv`;
      link.click();

      URL.revokeObjectURL(url);
    } catch (err) {
      setError('Download failed');
    }
  }, [data, fileName]);

  // Toggle functions
  const toggleDataPreview = () => setShowDataPreview(!showDataPreview);
  const toggleInputPreview = () => setShowInputPreview(!showInputPreview);

  // Limited data for preview
  const previewData = data.slice(0, 20);

  return (
    <GlassEffect variant="card">
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-3">
          <FileText className="w-6 h-6 text-blue-400" />
          <div>
            <h3 className="text-lg font-semibold text-white">{fileName}</h3>
            <p className="text-sm text-gray-300">
              {fileSize ? `${(fileSize / 1024).toFixed(1)} KB` : 'Unknown size'}
            </p>
          </div>
          {loading && (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 flex-wrap">
          {/* Input Preview Button */}
          <GlassEffect variant="button" onClick={toggleInputPreview}>
            <div className="flex items-center gap-2 px-2 py-1">
              <FileText className="w-4 h-4" />
              <span className="text-sm">
                {showInputPreview ? 'Hide Input' : 'Input Preview'}
              </span>
            </div>
          </GlassEffect>

          {/* Output Preview Button */}
          <GlassEffect
            variant="button"
            onClick={toggleDataPreview}
            disabled={loading || (!data.length && !error)}
          >
            <div className="flex items-center gap-2 px-2 py-1">
              {showDataPreview ? <EyeClosed className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              <span className="text-sm">
                {showDataPreview ? 'Hide Data' : 'Show Data'}
                {loading && ' (Loading...)'}
                {!data.length && !loading && ' (No data)'}
              </span>
            </div>
          </GlassEffect>

          {/* Download Button */}
          <GlassEffect
            variant="button"
            onClick={downloadFile}
            disabled={loading || !data.length}
          >
            <div className="flex items-center gap-2 px-2 py-1">
              <Download className="w-4 h-4" />
              <span className="text-sm">
                Download CSV
                {!data.length && !loading && ' (No data)'}
              </span>
            </div>
          </GlassEffect>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      {/* File Info */}
      <div className="mb-4 p-3 bg-white/10 rounded-lg">
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-300">Type:</span>
            <span className="text-white ml-2">{fileType || 'Unknown'}</span>
          </div>
          <div>
            <span className="text-gray-300">Status:</span>
            <span className="text-white ml-2">
              {loading ? 'Processing...' : error ? 'Error' : data.length > 0 ? 'Ready' : 'No data'}
            </span>
          </div>
          <div>
            <span className="text-gray-300">Rows:</span>
            <span className="text-white ml-2">{data.length}</span>
          </div>
        </div>
        {isExcel && (
          <div className="mt-2 text-xs text-gray-400">
            Excel file detected - {fileBlob ? 'File blob available' : 'No file blob'}
          </div>
        )}
      </div>

      {/* Input File Preview */}
      {showInputPreview && (
        <div className="mb-4">
          <h4 className="text-white font-medium mb-2">Original File Preview</h4>
          <div className="bg-white/10 rounded-lg p-4">
            {isImage && inputURL ? (
              <img
                src={inputURL}
                alt={fileName}
                className="max-w-full h-auto rounded"
                style={{ maxHeight: '300px' }}
              />
            ) : isPdf && inputURL ? (
              <iframe
                src={inputURL}
                title={fileName}
                className="w-full h-64 rounded"
              />
            ) : isText && inputURL ? (
              <div className="text-gray-200 text-sm">
                <p>Text file preview - {fileName}</p>
                <p>Size: {(fileSize / 1024).toFixed(1)} KB</p>
              </div>
            ) : (
              <div className="text-center text-gray-300">
                <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Preview not available for this file type</p>
                <p className="text-xs text-gray-400 mt-1">
                  {fileName} • {(fileSize / 1024).toFixed(1)} KB
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Data Preview */}
      {showDataPreview && data.length > 0 && (
        <div className="mt-4">
          <h4 className="text-white font-medium mb-2">Processed Data Preview</h4>
          <DataTable data={previewData} />
          {data.length > 20 && (
            <p className="text-center text-gray-300 text-sm mt-2">
              Showing 20 of {data.length} rows
            </p>
          )}
        </div>
      )}
    </GlassEffect>
  );
};

export default FilePreviewCard;



// Clean data table component
const DataTable = ({ data }) => {
  if (!data?.length) return null;

  const headers = Object.keys(data[0]);

  return (
    <div className="overflow-x-auto bg-white/10 rounded-lg">
      <table className="w-full text-sm">
        <thead>
          <tr className="border-b border-white/20">
            {headers.map((header, i) => (
              <th key={i} className="text-left p-3 text-gray-200 font-medium">
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, i) => (
            <tr key={i} className="border-b border-white/10">
              {headers.map((header, j) => (
                <td key={j} className="p-3 text-white max-w-[200px] truncate">
                  {String(row[header] || '')}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};


 