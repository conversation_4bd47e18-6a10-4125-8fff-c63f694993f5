import { useState } from 'react';
import './App.css';  // Ensure your CSS is up-to-date
import { ToastContainer } from 'react-toastify';
import { Routes, Route } from 'react-router-dom';
import FileUploadMain from './components/FileUploadMain';
import ExcelPreviewDownload from './components/FileDownload';
import FloatingChat from './components/FloatingChat';
import AnimatedBackground from './components/AnimatedBackground';
import { GlassFilter } from './components/GlassEffect';

function App() {
  const [outputFile, setOutputFile] = useState(null);
  const[isChatVisible, setIsChatVisible] = useState(false);
  return (
    <main className="h-screen w-screen relative">
      {/* Animated Background */}
      <AnimatedBackground />

      {/* Glass Effect SVG Filter */}
      <GlassFilter />

      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        closeOnClick
        pauseOnFocusLoss
        draggable
        pauseOnHover
        style={{ zIndex: 9999 }}
      />

      <section className='flex gap-[3px] w-screen px-3 transition-all relative z-10'>

        <div className=' flex-1 px-2'>
          <Routes>
            <Route path="/" element={<FileUploadMain setOutputFile={setOutputFile} />} />
            <Route path="/file-download" element={<ExcelPreviewDownload isChatVisible={isChatVisible} setIsChatVisible={setIsChatVisible} />} />
          </Routes>
        </div>

      <div className={`  `}>
                <FloatingChat isChatVisible={isChatVisible} setIsChatVisible={setIsChatVisible}/>
      </div>
      </section>
    </main>
  );
}

export default App;
