import React, { useState } from 'react';
import { Loader2, LogIn } from 'lucide-react'; // Assuming lucide-react is installed
import { useNavigate } from 'react-router-dom';  // Import useNavigate
import { useFileContext } from '../providers/FileProvider';  // Import useFileContext hook
import { uploadFile } from '../queries/apiHelper';
import { dummyTextLines } from '../helpers/dummyHelpers';
import  tcsLogo from '../assets/tata-logo-1.svg'
import { toast } from 'react-toastify';
import GlassEffect from './GlassEffect';
import TextScramble from './TextScramble';
import ParticleEffect from './ParticleEffect';
const FileUploadMain = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [uploadError, setUploadError] = useState(null); // For error handling
  const [selectedFile, setSelectedFile] = useState();  // Use state for file input
  const [uploadMode, setUploadMode] = useState('folder'); // 'folder' or 'files'
  const [processingTime, setProcessingTime] = useState(0);
  const [processingStartTime, setProcessingStartTime] = useState(null);

  const { setInputFiles ,  setOutputFile } = useFileContext();  // Get setOutputFile from context
  const navigate = useNavigate();  // Initialize the navigate function

 
// Upload a single file — no loading, no toast, no UI side-effects
const handleUpload = async (file) => {
  try {
    const resultCsvFile = await uploadFile(file);
    console.log('✅ File upload successful:', file.name);
    console.log('📄 Result details:', {
      type: resultCsvFile?.constructor?.name,
      size: resultCsvFile?.size,
      isBlob: resultCsvFile instanceof Blob,
      hasData: !!resultCsvFile,
      actualValue: resultCsvFile
    });

    // Additional debugging for handwritten files
    if (file.type.startsWith('image/') || file.name.toLowerCase().includes('handwritten')) {
      console.log('🖼️ Handwritten/Image file processing result:', {
        fileName: file.name,
        fileType: file.type,
        backendResponse: resultCsvFile,
        responseType: typeof resultCsvFile,
        isValidResponse: !!resultCsvFile
      });
    }

    // Check if we got a valid response (Blob or other valid data)
    if (resultCsvFile) {
      if (resultCsvFile instanceof Blob) {
        if (resultCsvFile.size > 0) {
          console.log('✅ Valid Blob response with size:', resultCsvFile.size);
          return resultCsvFile;
        } else {
          console.log('⚠️ Empty Blob received for:', file.name);
          return null;
        }
      } else {
        // Non-Blob response (could be other valid data)
        console.log('✅ Non-Blob response received for:', file.name);
        return resultCsvFile;
      }
    } else {
      console.log('⚠️ No response received for:', file.name);
      return null;
    }
  } catch (error) {
    console.log(' File upload failed:', file.name, error);
    return null; // Handle failure gracefully
  }
};

// Upload multiple files — manages loading, toasts, and final navigation
const handleFileUploads = async (files) => {
  resetInput();
  if (!files || files.length === 0) return;

  console.log(' Starting batch upload for', files.length, 'files');
  setIsLoading(true);
  setUploadError(null);
  setProcessingStartTime(Date.now());
  setProcessingTime(0);

  // Start processing time counter
  const timeInterval = setInterval(() => {
    if (processingStartTime) {
      setProcessingTime(Math.floor((Date.now() - processingStartTime) / 1000));
    }
  }, 1000);

  try {
    const results = [];
    let successCount = 0;
    let failureCount = 0;

    for (const file of files) {
      console.log(` Processing file ${successCount + failureCount + 1}/${files.length}:`, file.name);
      const processedFile = await handleUpload(file);

      console.log('🔍 Checking processedFile result:', {
        hasResult: !!processedFile,
        resultType: typeof processedFile,
        isBlob: processedFile instanceof Blob,
        size: processedFile?.size,
        fileName: file.name
      });

      if (processedFile) {
        results.push({
          'file': processedFile,
          'input_file': file
        });
        successCount++;
        console.log(`✅ File ${successCount + failureCount}/${files.length} processed successfully:`, file.name);
      } else {
        failureCount++;
        console.log(`❌ File ${successCount + failureCount}/${files.length} failed:`, file.name);
        console.log('❌ Failure reason: processedFile is', processedFile);
      }
    }

    console.log('📊 Batch processing complete:', {
      total: files.length,
      successful: successCount,
      failed: failureCount,
      results: results.length
    });

    if (results.length > 0) {
      console.log('✅ Setting output files and navigating to results page');
      setOutputFile(results);

      toast.success(`${results.length} file(s) processed successfully!`, {
        autoClose: 500,
        position: "top-right",
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });

      await new Promise((resolve) => setTimeout(resolve, 500)); // short delay
      navigate('/file-download');
    } else {
      console.log('❌ No files were successfully processed');
      setUploadError(`No files were successfully processed. ${failureCount} file(s) failed.`);
    }
  } catch (err) {
    console.error('💥 Unexpected error during uploads:', err);
    setUploadError('Something went wrong. Please try again.');
  } finally {
    setIsLoading(false);
    setProcessingStartTime(null);
    setProcessingTime(0);
    if (timeInterval) {
      clearInterval(timeInterval);
    }
  }
};

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);

    console.log("Total files selected:", files.length);

    files.forEach((file) => {
      console.log("Name:", file.name);
      console.log("Full Path:", file.webkitRelativePath);
      console.log("Type:", file.type);
      console.log("Size:", file.size, "bytes");
    });

    if (files.length > 0) {
      setSelectedFile(files);
      handleFileUploads(files);
    }
  };

  const resetInput = () => {
    setSelectedFile(null); // Reset the file state
    const inputFile =  document.getElementById('fileInput');
    const inputFolder = document.getElementById('folderInput');
    if (inputFile){
        inputFile.value = null;
    } // Reset the input field
    else if (inputFolder) {      
      inputFolder.value = null; // Reset the input field
    }

  };

  // Define the phrases for the scramble effect
  const scrambleDescriptions = [
    "Extract Data from PDFs Invoices",
    "Extract Data from handwritten invoices",
    "With multilanguage Support",
    "And Chat with Your Documents"
  ];

  return (
    <div className="h-screen flex flex-col items-center justify-center p-6 overflow-hidden">

      {/* Full Width Title with Particle Effect */}
      <div className="w-full mb-8 relative popup-enter">
        {/* Particle Effect Background */}
        <ParticleEffect className="absolute inset-0 z-0" />

        <GlassEffect variant="card" popup={true} popupDelay="0.1s" className="w-full px-8 py-8 relative z-10 stagger-popup">
          <h1 className="text-4xl md:text-6xl font-bold text-white text-center mb-6 fade-scale-in" style={{ animationDelay: '0.2s' }}>
            Invoice Data Extractor
          </h1>

          {/* Scramble Effect Description */}
          <div className="text-center fade-scale-in" style={{ animationDelay: '0.4s' }}>
            <TextScramble
              phrases={scrambleDescriptions}
              className="text-lg md:text-2xl font-light text-gray-200"
              delay={2500}
            />
          </div>
        </GlassEffect>
      </div>

      {/* Upload Mode Toggle */}
      <div className="mb-40 flex gap-4 popup-enter" style={{ animationDelay: '0.2s' }}>
        <GlassEffect
          variant="button"
          popup={true}
          popupDelay="0.3s"
          className={`px-8 py-4 cursor-pointer transition-all duration-500 ease-out transform hover:scale-105 hover:-translate-y-1 slide-in-left ${uploadMode === 'folder' ? 'bg-blue-500/30 border-blue-400 scale-105' : 'hover:bg-white/15'}`}
          onClick={() => setUploadMode('folder')}
        >
          <span className="text-white font-medium text-lg">Upload Folder</span>
        </GlassEffect>

        <GlassEffect
          variant="button"
          popup={true}
          popupDelay="0.4s"
          className={`px-8 py-4 cursor-pointer transition-all duration-500 ease-out transform hover:scale-105 hover:-translate-y-1 slide-in-right ${uploadMode === 'files' ? 'bg-blue-500/30 border-blue-400 scale-105' : 'hover:bg-white/15'}`}
          onClick={() => setUploadMode('files')}
        >
          <span className="text-white font-medium text-lg">Upload Files</span>
        </GlassEffect>
      </div>

      {/* Upload Button */}
      <div className="mb-20 popup-enter" style={{ animationDelay: '0.4s' }}>
        {uploadMode === 'folder' ? (
          <GlassEffect
            variant="button"
            disabled={isLoading}
            popup={true}
            popupDelay="0.6s"
            className="hover:scale-110 hover:-translate-y-2 transition-all duration-500 ease-out transform hover:shadow-2xl active:button-press gentle-bounce"
          >
            <label className="cursor-pointer flex items-center gap-3 px-12 py-6">
              {isLoading ? (
                <>
                  <Loader2 className="w-6 h-6 animate-spin" />
                  <span className="text-white font-semibold text-xl">Processing...</span>
                </>
              ) : (
                <>

                  <span className="text-white font-semibold text-xl">Select Folder</span>
                </>
              )}
              <input
                type="file"
                webkitdirectory=""
                multiple
                onChange={handleFileChange}
                accept=".txt,.pdf,.doc,.docx,.jpg,.jpeg,.png,image/*,.xlsx,.xls,.csv"
                className="hidden"
                disabled={isLoading}
              />
            </label>
          </GlassEffect>
        ) : (
          <GlassEffect
            variant="button"
            disabled={isLoading}
            popup={true}
            popupDelay="0.6s"
            className="hover:scale-110 hover:-translate-y-2 transition-all duration-500 ease-out transform hover:shadow-2xl active:button-press gentle-bounce"
          >
            <label className="cursor-pointer flex items-center gap-3 px-12 py-6">
              {isLoading ? (
                <>
                  <Loader2 className="w-6 h-6 animate-spin" />
                  <span className="text-white font-semibold text-xl">Processing...</span>
                </>
              ) : (
                <>
                  <span className="text-white font-semibold text-xl">Select Files</span>
                </>
              )}
              <input
                type="file"
                multiple
                onChange={handleFileChange}
                accept=".txt,.pdf,.doc,.docx,.jpg,.jpeg,.png,image/*,.xlsx,.xls,.csv"
                className="hidden"
                disabled={isLoading}
              />
            </label>
          </GlassEffect>
        )}
      </div>



      {/* Error Message */}
      {uploadError && (
        <div className="mt-6 error-popup">
          <GlassEffect variant="card" className="p-4 bg-red-500/20 border-red-400/30">
            <span className="text-red-200 font-medium">{uploadError}</span>
          </GlassEffect>
        </div>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center modal-backdrop-enter">
          <GlassEffect variant="card" className="p-8 text-center max-w-md popup-enter">
            <Loader2 className="w-12 h-12 animate-spin text-blue-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">Processing Files</h3>
            <p className="text-gray-300 text-sm mb-4">
              Handwritten Invoices OR Multiple Invoices may take several minutes for OCR processing...
            </p>
            {processingTime > 0 && (
              <div className="text-sm text-blue-300 mb-2">
                ⏱️ Processing time: {Math.floor(processingTime / 60)}:{(processingTime % 60).toString().padStart(2, '0')}
              </div>
            )}
            <div className="text-xs text-gray-400">
               Extended timeout: Up to 5 minutes for complex files
            </div>
            {processingTime > 120 && (
              <div className="text-xs text-yellow-400 mt-2">
                 Long processing time detected - backend may still be working
              </div>
            )}
          </GlassEffect>
        </div>
      )}

    </div>
  );
};

export default FileUploadMain;


