import { useState, useEffect, useRef } from "react";
import ChatB<PERSON>ble from "./ChatBubble";
import ChatInput from "./ChatInput";
import {  handleSend } from "../../queries/chatbotHelper";
// import aiLogo from '../../../public/ai-svgrepo-com.svg';

const ChatMainUi = () => {
  const [textInput, setTextInput] = useState("");
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([
    "What are the items in the invoices",
    "How much was the overall cost of all items",
    "Summerise the given invoice"
  ]);

 const [visibleSuggestions, setVisibleSuggestions] = useState([...suggestions]);
 
  const messagesEndRef = useRef(null);

  // Apply the light theme by default
  useEffect(() => {
    document.documentElement.classList.add("light");
  }, []);

    useEffect(() => {
  if (textInput === "") {
    setVisibleSuggestions([...suggestions]);
  }
}, [textInput]);

  useEffect(() => {
    if (messagesEndRef.current) {
      const scrollToBottom = () => {
        const scrollHeight = messagesEndRef.current.scrollHeight;
        const clientHeight = messagesEndRef.current.clientHeight;
        const maxScrollTop = scrollHeight - clientHeight;

        const start = messagesEndRef.current.scrollTop;
        const end = maxScrollTop;

        // Calculate the distance to scroll
        const distance = Math.abs(end - start);

        // Set duration based on distance
        const duration = Math.min(distance / 2, 1000);  // Adjust 1000 to tweak maximum duration
        let startTime = null;

        const animateScroll = (currentTime) => {
          if (!startTime) startTime = currentTime;
          const elapsed = currentTime - startTime;

          // Calculate the progress of the scroll
          const progress = Math.min(elapsed / duration, 1);  // Ensure it doesn't go over 1

          // Apply the scroll
          messagesEndRef.current.scrollTop = start + (end - start) * progress;

          // If progress isn't complete, continue the animation
          if (progress < 1) {
            requestAnimationFrame(animateScroll);
          }
        };

        requestAnimationFrame(animateScroll);
      };

      scrollToBottom();
    }
  }, [messages]);

  return (
    <main className="h-full flex flex-col relative rounded-xl bg-white/20 backdrop-blur-lg border border-white/30">
      {/* Chat Container */}
      <div className="flex flex-col h-full w-full">
        <h1 className="text-center text-lg font-bold p-4 text-white border-b border-white/20">
          💬 Chat Assistant
        </h1>

        {/* Scrollable Message Area */}
        <div
          className="mt-[20px] flex-1 scrollbar-hide overflow-y-auto p-4 py-[30px] space-y-8 relative"
          ref={messagesEndRef}
        >
          {/* Logo Section (Overlay Center) */}
          {/* <div
            className={`flex flex-col items-center space-y-2 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-500 ease-in-out ${
              messages.length > 0 ? "hidden" : "blur-0"
            }`}
          >
            <img src={aiLogo} alt="Leo Chatbot Logo" className="w-16 h-16" />
            {messages.length === 0 && (
              <p className=" vertical-gradient-text text-center text-gray-600 text-lg">
                Welcome to Ide Bot!
              </p>
            )}
            <p
              className={`text-lg text-gray-700 vertical-gradient-text ${
                messages.length > 0 ? "hidden" : ""
              }`}
            ></p>
          </div> */}

          {/* Chat Bubbles */}
          {messages.map((msg, i) => {
            console.log('message is ', msg );
            
            if ((  msg.text === '') && !msg.isUser) {
              msg.text = 'try a different prompt';
            }
            return (
              <ChatBubble
                key={i + msg}
                message={msg.text}
                output={msg.output}
                isUser={msg.isUser}
              />
            );
          })}
        </div>

        

        {/* Input Area Fixed at Bottom */}
        <footer className="">
          {/* Suggestions Section */}
        {messages.length === 0 && (
          <div className="flex flex-wrap gap-2 justify-center p-4">
            {visibleSuggestions.map((suggestion, index) => (
              <button
                key={index}
                className="px-3 py-2 bg-white/20 text-white text-sm rounded-lg border border-white/30
                         hover:bg-white/30 transition-colors duration-200"
                onClick={() => {
                  setTextInput(suggestion);
                  setVisibleSuggestions(suggestions.filter(item => item !== suggestion));
                }}
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}

          <ChatInput
            isLoading={isLoading}
            textInput={textInput}
            setTextInput={setTextInput}
            handleSend={() =>
              handleSend(textInput, setTextInput, setIsLoading, setMessages)
            }
          />
        </footer>
      </div>
    </main>
  );
};

export default ChatMainUi;
