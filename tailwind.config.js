/** @type {import('tailwindcss').Config} */
import DefaultTheme from 'tailwindcss/defaultTheme';

export default {
  content: [
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    screens: {
      'xs': '555px', // Custom XS screen
      ...DefaultTheme.screens, // Default screen sizes
    },
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      colors: {
        primary: '#4B5D67', // Custom primary color (example)
      },
      // 👇 ADD THIS SECTION FOR ANIMATIONS 👇
      keyframes: {
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
      },
      animation: {
        'fade-in-up': 'fadeInUp 0.3s ease-out forwards',
      },
      // ☝️ END OF ANIMATION SECTION ☝️
    },
  },
  plugins: [
    // Add these if you want rich text or custom forms
    // require('@tailwindcss/typography'),
    // require('@tailwindcss/forms'),
  ],
  
 };